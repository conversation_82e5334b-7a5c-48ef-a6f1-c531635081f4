import express from "express";
import { getDashboardStats } from "../../../controller/admin/dashboard.controller";
import { storageData } from "../../../utils/services/multer";
import { authAdminMiddleware } from "../../../middlewares/authAdminMiddleware";
import { getDoctorsWithPatientCount } from "../../../controller/admin/dashboard.controller";


const router = express.Router();
const upload = storageData("dashboard");

router.get("/dashboard", authAdminMiddleware, upload.none(), getDashboardStats);
router.get('/doctors-patient-count', authAdminMiddleware, getDoctorsWithPatientCount);


export default router;
