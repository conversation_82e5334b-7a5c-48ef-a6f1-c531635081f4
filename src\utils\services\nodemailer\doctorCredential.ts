import dotenv from "dotenv";

dotenv.config();

const {
  SMTP_HOST = "smtp-relay.brevo.com",
  SMTP_PORT = "587",
  SMTP_SECURE = "false",
  SMTP_USER = "<EMAIL>",
  SMTP_PASS = "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",
  EMAIL_FROM = "<EMAIL>"
} = process.env;

// Removed Nodemailer and transporter setup

interface DoctorCredentials {
  email: string;
  password: string;
  name: string;
}

interface PasswordResetData {
  email: string;
  name: string;
  password: string;
}

export const sendDoctorCredentialEmail = async (doctorData: DoctorCredentials) => {
  const { email, password, name } = doctorData;

  const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Welcome to Orthodontic Platform</h2>
        <p>Hello ${name},</p>
        <p>Your doctor account has been created. Below are your login credentials:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please log in and change your password immediately for security purposes.</p>
        <p>If you didn't expect this email, please contact the admin.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `;

  const payload = {
    sender: {
      name: "Orthodontic Team",
      email: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
    },
    to: [
      {
        email,
        name,
      },
    ],
    subject: "Your Doctor Account Credentials",
    htmlContent,
  };

  // Debug print for API key
  console.log("BREVO_API_KEY:", process.env.BREVO_API_KEY);

  try {
    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": process.env.BREVO_API_KEY as string,
      },
      body: JSON.stringify(payload),
    });

    const data = (await response.json()) as { messageId?: string };

    console.log("✅ Brevo Response:", data);

    if (data.messageId) {
      console.log(`✅ Credential email sent to ${email}`);
    } else {
      console.error("❌ Failed to send credential email:", data);
      throw new Error("Brevo API failed");
    }
  } catch (error) {
    console.error("❌ Brevo API error:", error);
    throw error;
  }
}

export const sendPasswordResetEmail = async (resetData: PasswordResetData) => {
  const { email, name, password } = resetData;

  const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Password Reset Successful</h2>
        <p>Hello ${name},</p>
        <p>Your password has been reset. Here are your new login details:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please change your password after logging in for security reasons.</p>
        <p>If you didn't request this reset, please contact support immediately.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `;

  const payload = {
    sender: {
      name: "Orthodontic Team",
      email: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
    },
    to: [
      {
        email,
        name,
      },
    ],
    subject: "Your New Password",
    htmlContent,
  };

  // Debug print for API key
  console.log("BREVO_API_KEY:", process.env.BREVO_API_KEY);

  try {
    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": process.env.BREVO_API_KEY as string,
      },
      body: JSON.stringify(payload),
    });

    const data = (await response.json()) as { messageId?: string };

    console.log("✅ Brevo Response:", data);

    if (data.messageId) {
      console.log(`✅ Password reset email sent to ${email}`);
    } else {
      console.error("❌ Failed to send password reset email:", data);
      throw new Error("Brevo API failed");
    }
  } catch (error) {
    console.error("❌ Brevo API error:", error);
    throw error;
  }
};

