import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import fs from "fs";
import asyncHand<PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import validate from "../../validations";
import { loginSchema, registerSchema } from "../../validations/auth.validation";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import { changePasswordSchema } from "../../validations/auth.validation";
import { getSignedJwt } from "../../utils/services/jwt";
import { sendRegistrationEmail } from "../../utils/services/nodemailer/register";
import { sendForgotPasswordEmail } from "../../utils/services/nodemailer/forgetPassword";
import { TABLE } from "../../utils/Database/table";
import { sendVerificationEmail } from "../../utils/services/nodemailer/emailVerify";
import { resendVerificationEmail } from "../../utils/services/nodemailer/resendOtp";
import { UserRole } from "../../utils/enums/users.enum";
import { createUserSchema } from "../../validations/user.validation";
import {
  sendDoctorCredentialEmail,
  sendPasswordResetEmail,
} from "../../utils/services/nodemailer/doctorCredential";
import { sendEmailChangeVerification } from "../../utils/services/nodemailer/emailChange";
import jwt from "jsonwebtoken";
export const createUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (
        req.user?.role !== UserRole.ADMIN &&
        req.user?.role !== UserRole.SUPERADMIN
      ) {
        sendResponse(res, 403, "Only admins can create new users", false);
        return;
      }

      const body = { ...req.body };

      const doctorRole = await db(TABLE.ROLES).where("role_name", UserRole.DOCTOR).first();
      const specialistRole = await db(TABLE.ROLES).where("role_name", UserRole.SPECIALIST).first();

      const isDoctorOrSpecialist =
        body.role_id == doctorRole?.id ||
        body.role === UserRole.DOCTOR ||
        body.role_id == specialistRole?.id ||
        body.role === UserRole.SPECIALIST;

      if (isDoctorOrSpecialist) {
        delete body.password;
      }

      const validationResult = validate(createUserSchema, body, res);
      if (!validationResult.success) {
        sendResponse(res, 400, "Validation error", false);
        return;
      }

      const { first_name, last_name, email, role_id, username, password } =
        validationResult.data;

      const userRoleId = role_id || doctorRole?.id;

      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      if (username) {
        const existingUsername = await db(TABLE.USERS).where({ username }).first();
        if (existingUsername) {
          sendResponse(res, 400, "Username already exists", false);
          return;
        }
      }

      const roleRecord = await db(TABLE.ROLES).where("id", userRoleId).first();
      if (!roleRecord) {
        sendResponse(res, 400, "Invalid role specified", false);
        return;
      }

      if (
        ![UserRole.DOCTOR, UserRole.SPECIALIST].includes(roleRecord.role_name) &&
        !password
      ) {
        sendResponse(res, 400, "Password is required for non-doctor/specialist roles", false);
        return;
      }

      const generateRandomPassword = () => {
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let pass = "";
        for (let i = 0; i < 8; i++) {
          pass += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return pass;
      };

      const finalPassword =
        [UserRole.DOCTOR, UserRole.SPECIALIST].includes(roleRecord.role_name)
          ? generateRandomPassword()
          : password;

      const hashedPassword = await bcrypt.hash(finalPassword as string, 10);

      const insertData = {
        first_name,
        last_name,
        email,
        username: username || null,
        is_verified: true,
        password: hashedPassword,
        role_id: roleRecord.id,
      };

      // 🟡 TRANSACTION STARTS HERE
      await db.transaction(async (trx) => {
        const [newUser] = await trx(TABLE.USERS)
          .insert(insertData)
          .returning([
            "id",
            "first_name",
            "last_name",
            "email",
            "username",
            "role_id",
            "is_active",
            "is_verified",
          ]);

        const userData = {
          ...newUser,
          role: roleRecord.role_name,
        };

        const rolesRequiringEmail = [UserRole.DOCTOR, UserRole.SPECIALIST];

        if (rolesRequiringEmail.includes(roleRecord.role_name)) {
          try {
            await sendDoctorCredentialEmail({
              email,
              password: finalPassword as string,
              name: `${first_name} ${last_name}`,
            });
          } catch (emailError) {
            console.error("Failed to send email:", emailError);
            // ❌ Throw to rollback
            throw new Error("Email delivery failed. Please try creating the user again.");
          }
        }

        // ✅ If all successful
        sendResponse(
          res,
          201,
          `${roleRecord.role_name} created successfully and credentials sent via email`,
          true,
          userData
        );
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);


export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, username, password, remember } = req.body;
  console.log("Login request body:", typeof remember);
  try {
    // Traditional email/password login
    const validationResult = validate(loginSchema, req.body, res);
    if (!validationResult.success) {
      sendResponse(res, 400, "Validation error", false);
      return;
    }

    // Allow login with either email or username
    const query = db(TABLE.USERS).where({ is_deleted: false });

    if (email) {
      query.where({ email });
    } else if (username) {
      query.where({ username });
    } else {
      sendResponse(res, 400, "Email or username is required", false);
      return;
    }

    let user = await query.first();

    if (!user) {
      sendResponse(res, 400, "Invalid email/username or password", false);
      return;
    }

    if (!user.is_active) {
      sendResponse(res, 403, "Your account is inactive.", false);
      return;
    }

    if (!user.is_verified) {
      await sendVerificationEmail(user, res);
      return;
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      sendResponse(res, 400, "Invalid email/username or password", false);
      return;
    }

    // Get role information
    if (user.role_id) {
      const role = await db(TABLE.ROLES).where("id", user.role_id).first();
      if (
        role &&
        role.role_name !== UserRole.SUPERADMIN &&
        role.role_name !== UserRole.ADMIN
      ) {
        user.role = role.role_name;
      } else {
        sendResponse(
          res,
          403,
          "You are not authorized to perform this action",
          false
        );
        return;
      }
    }

    const accessToken = getSignedJwt(user.id, remember);

    // Remove password from response
    delete user.password;

    if (user.profile_image) {
      user.profile_image = process.env.BASE_URL + user.profile_image;
    }

    sendResponse(res, 200, "Login successful", true, {
      accessToken,
      user,
    });
  } catch (err) {
    console.error(err);
    sendResponse(res, 500, "Internal server error", false, err);
  }
});
export const adminLogin = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  try {
    let user;

    // Traditional email/password login
    const validationResult = validate(loginSchema, req.body, res);
    if (!validationResult.success) {
      sendResponse(res, 400, "Validation error", false);
      return;
    }

    user = await db(`${TABLE.USERS} as u`)
      .leftJoin(`${TABLE.ROLES} as r`, "u.role_id", "r.id")
      .select(
        "u.id as id",
        "u.first_name",
        "u.last_name",
        "u.email",
        "u.password",
        "u.profile_image",
        "u.is_active",
        "u.is_verified",
        "u.is_deleted",
        "u.created_at",
        "u.updated_at",
        "u.username",
        "u.role_id",
        "r.role_name"
      )
      .where({ "u.email": email, "u.is_deleted": false })
      .first();

    if (!user) {
      sendResponse(res, 400, "Invalid email or password", false);
      return;
    }

    if (
      user.role_name != UserRole.SUPERADMIN &&
      user.role_name != UserRole.ADMIN
    ) {
      sendResponse(
        res,
        403,
        "You are not authorized to perform this action",
        false
      );
      return;
    }

    if (!user.is_active) {
      sendResponse(res, 403, "Your account is inactive.", false);
      return;
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      sendResponse(res, 400, "Invalid email or password", false);
      return;
    }

    const accessToken = getSignedJwt(user.id);

    // Remove password from response
    delete user.password;

    if (user.profile_image) {
      user.profile_image = process.env.BASE_URL + user.profile_image;
    }

    sendResponse(res, 200, "Login successful", true, {
      accessToken,
      user,
    });
  } catch (err) {
    console.error(err);
    sendResponse(res, 500, "Internal server error", false, err);
  }
});
export const verifyToken = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email, token } = req.body;

    // Validate input
    if (!email || !token) {
      sendResponse(res, 400, "Email and OTP are required.", false);
      return;
    }

    // Fetch the user
    const user = await db(TABLE.USERS)
      .where({ email, is_deleted: false })
      .first();
    if (!user) {
      sendResponse(res, 404, "Account not found.", false);
      return;
    }

    // Check if user is inactive
    if (!user.is_active) {
      sendResponse(res, 403, "Account is not inactive.", false);
      return;
    }

    // Fetch the reset token from the database
    const resetToken = await db(TABLE.PASSWORD_RESETS)
      .where({ email, token })
      .first();

    if (!resetToken) {
      sendResponse(res, 400, "Invalid or expired OTP.", false);
      return;
    }

    // Check if the OTP is expired (created more than 10 minutes ago)
    const tokenAge = Date.now() - new Date(resetToken.created_at).getTime();

    if (tokenAge > 10 * 60 * 1000) {
      sendResponse(res, 400, "OTP has been expired.", false);
      return;
    }

    await db(TABLE.PASSWORD_RESETS).where({ email }).delete();

    sendResponse(res, 200, "OTP has been verified.", true);
  } catch (error: any) {
    console.error("OTP verification error:", error);
    sendResponse(res, 500, error.message, false);
  }
});
export const forgetPassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email, username } = req.body;

      // Validate input
      if (!email && !username) {
        sendResponse(
          res,
          400,
          "Please provide either email or username",
          false
        );
        return;
      }

      // Find user by email or username
      const existUser = await db(TABLE.USERS)
        .where(function () {
          if (email) this.orWhere("email", email);
          if (username) this.orWhere("username", username);
        })
        .andWhere("is_deleted", false)
        .first();

      if (!existUser) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      // Check verification status
      if (!existUser.is_verified) {
        sendResponse(res, 403, "Account is not verified", false);
        return;
      }

      // Generate random password (same as createUser function)
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      // Create and hash new password
      const newPassword = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password in database
      await db(TABLE.USERS)
        .where("id", existUser.id)
        .update({ password: hashedPassword });

      // Send email with new password
      try {
        await sendPasswordResetEmail({
          email: existUser.email,
          name: `${existUser.first_name} ${existUser.last_name}`,
          password: newPassword,
        });

        sendResponse(
          res,
          200,
          "New password sent to your registered email",
          true
        );
      } catch (emailError) {
        console.error("Email sending failed:", emailError);
        sendResponse(
          res,
          200,
          "Password reset but failed to send email. Please contact support.",
          true
        );
      }
    } catch (err) {
      console.error(err);
      sendResponse(res, 500, "Internal server error", false, err);
    }
  }
);
export const changePassword = asyncHandler(
  async (req: Request, res: Response) => {
    const validationResult = validate(changePasswordSchema, req.body, res);

    if (!validationResult.success) {
      sendResponse(res, 400, "Validation error", false);
      return;
    }

    // Check if user is logged in
    const userId = req.user?.id;

    const { password, currentPassword } = req.body;

    const user = await db(TABLE.USERS)
      .where({ id: userId, is_deleted: false })
      .first();

    if (!user) {
      sendResponse(res, 400, "Account not found", false);
      return;
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);

    if (!isMatch) {
      sendResponse(res, 400, "Current password is incorrect", false);
      return;
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    await db("users")
      .where({ id: userId })
      .update({ password: hashedPassword });

    sendResponse(res, 200, "Password changed successfully", true);
    return;
  }
);
export const deleteAccount = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    sendResponse(res, 401, "Unauthorized", false);
    return;
  }

  try {
    const user = await db(TABLE.USERS)
      .where({ id: userId, is_deleted: false })
      .first();

    if (!user) {
      sendResponse(res, 404, "User not found", false);
      return;
    }

    if (user.profile_image) {
      fs.unlinkSync(`public/${user.profile_image}`);
    }

    await db(TABLE.USERS).where({ id: userId }).delete();

    sendResponse(res, 200, "Account deleted successfully", true);
  } catch (err) {
    console.error(err);
    sendResponse(res, 500, "Internal server error", false, err);
  }
}
);
export const checkSession = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Check if user is authenticated via middleware
      if (!req.user) {
        sendResponse(res, 401, "Session expired or invalid", false);
        return;
      }

      // Fetch latest user status from database
      const user = await db(TABLE.USERS)
        .where({
          id: req.user.id,
          is_deleted: false,
          is_active: true,
        })
        .first();

      if (!user) {
        sendResponse(res, 401, "User not found or account deactivated", false);
        return;
      }

      // Prepare safe user data for response
      const userData = {
        id: user.id,
        email: user.email,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        role_id: user.role_id,
        is_verified: user.is_verified,
        profile_image: user.profile_image
          ? process.env.BASE_URL + user.profile_image
          : null,
      };

      sendResponse(res, 200, "Active session found", true, userData);
    } catch (error: any) {
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const requestEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { newEmail } = req.body;

    if (!newEmail) {
      return sendResponse(res, 400, "New email is required", false);
    }

    const exists = await db(TABLE.USERS).where({ email: newEmail }).first();
    if (exists) {
      return sendResponse(res, 400, "Email already in use", false);
    }

    const user = await db(TABLE.USERS).where({ id: userId }).first();
    if (!user) {
      return sendResponse(res, 404, "User not found", false);
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId,
        newEmail,
      },
      process.env.JWT_SECRET || "secret",
      { expiresIn: "10m" }
    );

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await db(TABLE.EMAIL_CHANGES).insert({
      user_id: userId,
      new_email: newEmail,
      token,
      expires_at: expiresAt,
      is_verified: false,
    });

    await sendEmailChangeVerification({
      currentEmail: user.email,
      newEmail,
      token,
      name: `${user.first_name} ${user.last_name}`,
    });

    sendResponse(
      res,
      200,
      "Verification token sent to your current email",
      true
    );
  }
);
export const verifyEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;

    // 1) pull token from the query
    const token =
      typeof req.query.token === "string" ? req.query.token : null;
    if (!token) {
      return sendResponse(res, 400, "Token is required", false);
    }

    // 2) fetch the matching unverified request
    const record = await db(TABLE.EMAIL_CHANGES)
      .where({ user_id: userId, token, is_verified: false })
      .orderBy("created_at", "desc")
      .first();

    if (!record) {
      return sendResponse(res, 400, "Invalid or expired token", false);
    }

    // 3) check expiry
    if (new Date(record.expires_at).getTime() < Date.now()) {
      return sendResponse(res, 400, "Token expired", false);
    }

    // 4) mark it as verified
    await db(TABLE.EMAIL_CHANGES)
      .where({ id: record.id })
      .update({
        is_verified: true,
        verified_at: new Date(),
      });

    // 5) return the new_email from the DB record
    sendResponse(
      res,
      200,
      "Token verified. You can now confirm your new email.",
      true,
      { newEmail: record.new_email }
    );
  }
);
export const confirmEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { newEmail } = req.body;

    if (!newEmail) {
      return sendResponse(res, 400, "New email is required", false);
    }

    // Find a verified email change record for this user and newEmail
    const record = await db(TABLE.EMAIL_CHANGES)
      .where({ user_id: userId, new_email: newEmail, is_verified: true })
      .orderBy("verified_at", "desc")
      .first();

    if (!record) {
      return sendResponse(
        res,
        400,
        "No verified email change request found",
        false
      );
    }

    // Check if new email is already taken (race condition check)
    const exists = await db(TABLE.USERS).where({ email: newEmail }).first();
    if (exists) {
      return sendResponse(res, 400, "Email already in use", false);
    }

    // Update user's email
    await db(TABLE.USERS).where({ id: userId }).update({ email: newEmail });

    sendResponse(res, 200, "Email changed successfully", true);
  }
);
