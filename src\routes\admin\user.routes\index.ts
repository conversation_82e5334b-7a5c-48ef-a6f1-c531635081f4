import express from 'express';
import { storageData } from '../../../utils/services/multer';
import {
  destroyUserById,
  getAllDoctors,
  getAllUsers,
  getUsersById,
  updateUser,
  getAllPatientsWithDoctors,
  getPatientById
} from '../../../controller/admin/user.controller';

const router = express.Router();
const upload = storageData("users");

// Routes
router.get('/', getAllUsers);
router.patch('/:id', upload.single("profile_image"), updateUser);
router.delete('/:id', destroyUserById);
router.get('/doctors', getAllDoctors);
router.get('/patients', getAllPatientsWithDoctors);
router.get('/patients/:id', getPatientById);
router.get('/:id', getUsersById);

export default router;
