import { NextFunction, Request, Response } from "express";
import jwt, { JwtPayload } from "jsonwebtoken";
import { response } from "../utils/response";
import db from "../config/db";
import type { User } from "../utils/types/auth";
import { verifyToken } from "../utils/services/jwt";
import { sendResponse } from "../utils/helperFunctions/responseHelper";
import { TABLE } from "../utils/Database/table";
import { UserRole } from "../utils/enums/users.enum";

declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

export const authAdminMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = req.header("Authorization")?.replace("Bearer ", "");

    if (!token) {
      sendResponse(res, 401, "Unauthorized! please login first.", false);
      return;
    }

    const decoded = verifyToken(token);
    const userId = (decoded as JwtPayload).id;

    // Fetch user details with role information using JOIN
    const user = await db(TABLE.USERS)
      .select([
        `${TABLE.USERS}.*`,
        `${TABLE.ROLES}.role_name as role`
      ])
      .leftJoin(TABLE.ROLES, `${TABLE.USERS}.role_id`, '=', `${TABLE.ROLES}.id`)
      .where(`${TABLE.USERS}.id`, userId)
      .first();

    if (!user) {
      sendResponse(res, 404, "User account not found", false);
      return;
    }

    if (!user.is_verified) {
      sendResponse(res, 403, "Your account is not verified", false);
      return;
    }

    if (!user.is_active) {
      sendResponse(
        res,
        403,
        "User account has been disabled by the administrator",
        false
      );
      return;
    }

    // Check role using the role_name field we got from the JOIN
    if (user.role !== UserRole.SUPERADMIN && user.role !== UserRole.ADMIN) {
      sendResponse(
        res,
        403,
        "You are not authorized to perform this action",
        false
      );
      return;
    }

    req.user = user;
    next();
  } catch (error: any) {
    console.error("JWT verification error:", error.message);
    sendResponse(res, 401, "Unauthorized! please login first.", false);
    return;
  }
};