import dotenv from "dotenv";
dotenv.config();


const allowedOrigins: string[] = (
  process.env.ALLOWED_ORIGINS
    ? process.env.ALLOWED_ORIGINS.split(",").map(origin => origin.trim())
    : [
        process.env.FRONTEND_URL ?? "",
        process.env.BASE_URL ?? "",
        process.env.ADMIN_BASE_URL ?? "",
        process.env.RESET_BASE_URL ?? "",
      ]
).filter((origin) => origin);


const corsOptions = {
  origin: allowedOrigins,
  credentials: true,
};

export default corsOptions;
