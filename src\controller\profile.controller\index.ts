import { Request, Response } from "express";
import fs from "fs";
import async<PERSON>and<PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";

export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  try {
    // Check if the user exists
    const existingUser = await db(TABLE.USERS)
      .where({ id: req.user?.id, is_deleted: false })
      .first();

    if (!existingUser) {
      sendResponse(res, 400, "User account not found", false);
      return;
    }

    // Remove password and username from response
    delete existingUser.password;
    delete existingUser.username;

    if (existingUser.profile_image) {
      existingUser.profile_image =
        process.env.BASE_URL + existingUser.profile_image;
    }

    sendResponse(res, 200, "Registered successfully", true, existingUser);
  } catch (error) {
    console.error("Error get profile:", error);
    return sendResponse(res, 500, "Failed to get profile.", false);
  }
});

export const updateProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;
    try {
      // 1) Fetch user
      const user = await db(TABLE.USERS)
        .where({ id: userId, is_deleted: false })
        .first();
      if (!user) {
        return sendResponse(res, 404, "User not found", false);
      }

      // 2) Extract fields
      const { first_name, last_name, email, username } = req.body;

      // 3) Email uniqueness check
      if (email) {
        const emailExists = await db(TABLE.USERS)
          .where({ email, is_deleted: false })
          .whereNot("id", userId)
          .first();
        if (emailExists) {
          return sendResponse(res, 400, "Email already exists", false);
        }
      }

      // 4) Username uniqueness check
      if (username) {
        const usernameExists = await db(TABLE.USERS)
          .where({ username, is_deleted: false })
          .whereNot("id", userId)
          .first();
        if (usernameExists) {
          return sendResponse(res, 400, "Username already exists", false);
        }
      }

      // 5) Prepare update payload
      const updateData: any = {
        first_name,
        last_name,
        email,
        username,
        updated_at: new Date(),
      };

      // 6) Handle profile image
      if (req.file) {
        if (user.profile_image) {
          fs.unlinkSync(`public/${user.profile_image}`);
        }
        updateData.profile_image = "/user/" + req.file.filename;
      }

      // 7) Perform update
      await db(TABLE.USERS).where({ id: userId }).update(updateData);

      // 8) Fetch updated user and attach full image URL
      const updatedUser = await db(TABLE.USERS).where({ id: userId }).first();
      delete updatedUser.password;
      if (updatedUser.profile_image) {
        updatedUser.profile_image =
          process.env.BASE_URL + updatedUser.profile_image;
      }

      // 9) Send success response
      return sendResponse(
        res,
        200,
        "Profile updated successfully",
        true,
        updatedUser
      );
    } catch (err: any) {
      console.error("Error updating profile:", err);
      // clean error response
      return sendResponse(res, 500, "Failed to update profile", false);
    }
  }
);
  