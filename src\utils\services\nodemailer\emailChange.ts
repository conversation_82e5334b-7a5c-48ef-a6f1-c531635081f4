import nodemailer from "nodemailer";
import path from "path";
import fs from "fs";
import handlebars from "handlebars";

export const sendEmailChangeVerification = async ({
  currentEmail,
  newEmail,
  token,
  name,
}: {
  currentEmail: string;
  newEmail: string;
  token: string;
  name: string;
}) => {
  try {
    const templatePath = path.join(
      __dirname,
      "templates/emailChangeVerification.hbs"
    );
    const templateSource = fs.readFileSync(templatePath, "utf-8");
    const template = handlebars.compile(templateSource);

    // Use token only — no email in query
    const verifyLink = `${process.env.BASE_URL}/api/v1/auth/verify-email?token=${token}`;

    const html = template({
      name,
      currentEmail,
      newEmail,
      verifyLink,
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
    });

    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || "smtp-relay.brevo.com",
      port: Number(process.env.SMTP_PORT || 587),
      secure: process.env.SMTP_SECURE === "true", // Brevo uses 587 (false) or 465 (true)
      auth: {
        user: process.env.SMTP_USER || "<EMAIL>",
        pass: process.env.SMTP_PASS || "your_brevo_smtp_key",
      },
    });

    await transporter.sendMail({
      from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: currentEmail,
      subject: "Verify Your Email Change",
      html,
    });
  } catch (error) {
    console.error("Error sending email change verification:", error);
    throw error;
  }
};
