import { Router } from "express";
import AUTH<PERSON>UT<PERSON> from "./auth.routes";
import DOCTOR<PERSON>UT<PERSON> from "./doctor.routes";
import PERMISSIONROUTES from "./permission.routes";
import SPECIA<PERSON>ISTROUTES from "./specialist.routes";
import ADMINPLA<PERSON>OUT<PERSON> from "./admin/plan.routes";
import USERROUTES from "./admin/user.routes";
import { authAdminMiddleware } from "../middlewares/authAdminMiddleware";
const router = Router();

router.use("/auth", AUTHROUTES);
router.use("/admin", ADMINPLANROUTES);
router.use("/doctor", DOCTORROUTES);
router.use("/permissions", PERMISSIONROUTES);
router.use("/specialist", SPECIALISTROUTES);
router.use("/user", authAdminMiddleware, USERROUTES);

export default router;
