const { Client } = require('pg');

async function verifyFix() {
  const client = new Client({
    host: 'localhost',
    port: 5433,
    user: 'postgres',
    password: 'postgres',
    database: 'orthodontic_db'
  });

  try {
    await client.connect();
    console.log("✅ Connected to database");

    // Test the user_uuid generation logic directly
    console.log("🧪 Testing user_uuid generation logic...");

    // Generate a 7-digit user_uuid
    let user_uuid;
    do {
      user_uuid = Math.floor(1_000_000 + Math.random() * 9_000_000).toString();
      console.log("🎲 Generated user_uuid:", user_uuid);
      
      // Check if it already exists
      const existingResult = await client.query('SELECT id FROM users WHERE user_uuid = $1', [user_uuid]);
      if (existingResult.rows.length === 0) {
        console.log("✅ user_uuid is unique");
        break;
      } else {
        console.log("⚠️ user_uuid already exists, generating new one...");
      }
    } while (true);

    // Test inserting a user with user_uuid
    const testUser = {
      user_uuid: user_uuid,
      first_name: 'Test',
      last_name: 'User',
      email: `test_${Date.now()}@example.com`,
      username: `testuser_${Date.now()}`,
      password: '$2b$10$hashedpassword',
      is_verified: true,
      role_id: 1
    };

    console.log("🧩 Inserting test user with user_uuid...");
    
    const insertResult = await client.query(`
      INSERT INTO users (user_uuid, first_name, last_name, email, username, password, is_verified, role_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, user_uuid, first_name, last_name, email
    `, [
      testUser.user_uuid,
      testUser.first_name,
      testUser.last_name,
      testUser.email,
      testUser.username,
      testUser.password,
      testUser.is_verified,
      testUser.role_id
    ]);

    console.log("✅ User inserted successfully:");
    console.log("🎯 Returned data:", insertResult.rows[0]);

    // Verify the user_uuid was saved correctly
    const verifyResult = await client.query('SELECT * FROM users WHERE user_uuid = $1', [user_uuid]);
    
    if (verifyResult.rows.length > 0) {
      console.log("✅ user_uuid verification successful!");
      console.log("🔍 Full user record:", verifyResult.rows[0]);
    } else {
      console.log("❌ user_uuid verification failed!");
    }

    // Clean up test data
    await client.query('DELETE FROM users WHERE user_uuid = $1', [user_uuid]);
    console.log("🧹 Test data cleaned up");

  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await client.end();
  }
}

verifyFix();
