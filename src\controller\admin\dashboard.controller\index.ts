import { Request, Response } from "express";
import db from "../../../config/db";
import { TABLE } from "../../../utils/Database/table";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";

export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    // Count doctors
    const [{ count: doctorCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .count({ count: "*" });

    // Count patients
    const [{ count: patientCount }] = await db(TABLE.PATIENTS).count({ count: "*" });

    // Count employees
    const [{ count: employeeCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "employee")
      .count({ count: "*" });

    sendResponse(res, 200, "Dashboard stats fetched", true, {
      doctors: Number(doctorCount),
      patients: Number(patientCount),
      employees: Number(employeeCount),
    });
  } catch (error: any) {
    sendResponse(res, 500, error.message, false);
  }
};