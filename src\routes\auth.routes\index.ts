import { Router } from "express";
import { AUTH } from "../../utils/enums/auth.enum";
import {
  adminLogin,
  changePassword,
  deleteAccount,
  forgetPassword,
  login,
  // registerChef,
  createUser,
  // resendOTP,
  // resetPassword,
  verifyToken,
  checkSession,
  requestEmailChange,
  verifyEmailChange,
  confirmEmailChange,
  // resendOTP,
  // verifyAccount,
  // verifyToken,
} from "../../controller/auth.controller";
import { storageData } from "../../utils/services/multer";
import { authMiddleware } from "../../middlewares/authMiddleware";
import { getProfile, updateProfile } from "../../controller/profile.controller";
import { authAdminMiddleware } from "../../middlewares/authAdminMiddleware";
import asyncHandler from "../../middlewares/trycatch";

const router = Router();

const upload = storageData("user");
// const uploadChefDocs = storageData("chef_docs");

router.post(AUTH.CREATE_USER, authAdminMiddleware, upload.none(), createUser);
router.post(AUTH.LOGIN_USER, upload.none(), login);
router.post(AUTH.LOGIN_ADMIN, upload.none(), adminLogin);
router.post(AUTH.VERIFY_TOKEN, upload.none(), verifyToken);
router.post(AUTH.FORGOT_PASSWORD, upload.none(), forgetPassword);
router.delete(AUTH.DELETE_ACCOUNT, authMiddleware, deleteAccount);
router.put(AUTH.CHANGE_PASSWORD, authMiddleware, upload.none(), changePassword);
router.get(AUTH.PROFILE, authMiddleware, getProfile);
router.get(AUTH.CHECK_SESSION, authMiddleware, upload.none(), checkSession);
router.post("/email/change/request", authMiddleware, upload.none(), requestEmailChange);
router.post("/email/change/verify", authMiddleware, upload.none(), verifyEmailChange);
router.post("/email/change/confirm", authMiddleware, upload.none(), confirmEmailChange);
router.get(
  "/verify-email",           // ← the same path your link hits
  authMiddleware,
  verifyEmailChange          // ← was accidentally pointing at confirmEmailChange
);

// Public verification endpoint

router.put(
  AUTH.PROFILE,
  authMiddleware,
  upload.single("profile_image"),
  updateProfile
);

export default router;
