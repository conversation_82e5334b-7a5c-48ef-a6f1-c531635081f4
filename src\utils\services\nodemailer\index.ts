import nodemailer from "nodemailer";
import { config } from "dotenv";

config();

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || "smtp-relay.brevo.com",
  port: parseInt(process.env.SMTP_PORT || "587"),
  secure: process.env.SMTP_SECURE === "true", // Brevo uses 587 (false) or 465 (true)
  auth: {
    user: process.env.SMTP_USER || "<EMAIL>",
    pass: process.env.SMTP_PASS || "your_brevo_smtp_key",
  },
});

export default transporter;
